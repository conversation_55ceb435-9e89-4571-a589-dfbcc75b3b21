/**
 * HTML encoding utilities for safe display of user-generated content
 * Prevents XSS attacks by properly encoding HTML entities
 */

/**
 * Encode HTML entities to prevent XSS attacks
 */
export function encodeHtml(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Encode HTML attributes to prevent XSS in attribute values
 */
export function encodeHtmlAttribute(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\r/g, '&#13;')
    .replace(/\n/g, '&#10;');
}

/**
 * Safe text display component props
 */
export interface SafeTextProps {
  text: string;
  maxLength?: number;
  allowLineBreaks?: boolean;
  className?: string;
}

/**
 * Safely encode and truncate text for display
 */
export function safeDisplayText(
  text: string, 
  options: {
    maxLength?: number;
    allowLineBreaks?: boolean;
    preserveWhitespace?: boolean;
  } = {}
): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  const { maxLength = 1000, allowLineBreaks = true, preserveWhitespace = false } = options;

  let safeText = encodeHtml(text);

  // Handle line breaks
  if (allowLineBreaks) {
    safeText = safeText.replace(/\n/g, '<br>');
  }

  // Handle whitespace
  if (preserveWhitespace) {
    safeText = safeText.replace(/  /g, '&nbsp;&nbsp;');
  }

  // Truncate if needed
  if (safeText.length > maxLength) {
    safeText = safeText.substring(0, maxLength) + '...';
  }

  return safeText;
}

/**
 * Safely encode user names for display
 */
export function safeUserName(name: string | null | undefined): string {
  if (!name) {
    return 'Anonymous';
  }
  return encodeHtml(name);
}

/**
 * Safely encode email addresses for display
 */
export function safeEmail(email: string | null | undefined): string {
  if (!email) {
    return '';
  }
  return encodeHtml(email);
}

/**
 * Safely encode URLs for href attributes
 */
export function safeUrl(url: string | null | undefined): string {
  if (!url) {
    return '#';
  }
  
  // Basic URL validation
  try {
    const urlObj = new URL(url);
    // Only allow http and https protocols
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      return '#';
    }
    return encodeHtmlAttribute(url);
  } catch {
    return '#';
  }
}

/**
 * React component for safely displaying user text
 */
export function SafeText({ 
  text, 
  maxLength = 1000, 
  allowLineBreaks = true, 
  className = '' 
}: SafeTextProps) {
  const safeText = safeDisplayText(text, { maxLength, allowLineBreaks });
  
  return (
    <span 
      className={className}
      dangerouslySetInnerHTML={{ __html: safeText }}
    />
  );
}
